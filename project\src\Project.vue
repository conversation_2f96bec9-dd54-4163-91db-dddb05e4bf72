<script setup lang="ts">
defineOptions({ name: 'Project' })
import MenuComp from './components/menuComp'
import TopComp from './components/topComp'
import MainMenu from './components/mainMenu'
import { useRouter, useRoute } from 'vue-router'

const route = useRoute()
const router = useRouter()

import { projectStore } from './store/index'
const store = projectStore()
import { storeToRefs } from 'pinia'
const { currentMenuUrl, pageJump, breadcrumbList, topCurrentMenu, currentLeftMenu } = storeToRefs(store)

import { listAllMenuForSecurity } from './api/home'

const leftMenuList = ref() //左侧二级菜单
const thirdMenuList = ref() //三级菜单

const menuList = ref()
const userInfo = ref()

// 初始化菜单状态
const initializeMenuState = (menus: any[], user: any) => {
  menuList.value = menus
  userInfo.value = user
  window.sessionStorage.setItem('userLoginName', user.userLoginName)

  // 获取当前路由路径
  const currentRoutePath = route.path

  // 先设置默认的顶部菜单（如果没有保存状态）
  if (!topCurrentMenu.value || !topCurrentMenu.value.menuId) {
    store.setTopCurrentMenu(menus[0])
  }

  // 恢复顶部菜单对应的左侧菜单
  const activeTopMenu = menus.find((menu: any) => menu.menuId === topCurrentMenu.value?.menuId) || menus[0]

  if (activeTopMenu.menuUrl === '/HomePage') {
    leftMenuList.value = '/HomePage'
  } else {
    leftMenuList.value = activeTopMenu.children || []

    // 恢复左侧菜单选中状态
    if (currentLeftMenu.value && currentLeftMenu.value.menuId) {
      const savedLeftMenu = activeTopMenu.children?.find((menu: any) => menu.menuId === currentLeftMenu.value.menuId)
      if (savedLeftMenu) {
        thirdMenuList.value = savedLeftMenu.children || []
      }
    }
  }

  // 处理当前路由状态
  if (currentRoutePath && currentRoutePath !== '/' && currentRoutePath !== '/HomePage') {
    // 如果当前有具体路由，但currentMenuUrl为空，设置它
    if (!currentMenuUrl.value) {
      const currentMenu = findMenuByUrl(currentRoutePath, menus)
      if (currentMenu) {
        store.setCurrentMenuUrl(currentRoutePath)
        // 确保面包屑中有当前菜单
        const breadcrumbExists = breadcrumbList.value.some((item: any) => item.menuUrl === currentRoutePath)
        if (!breadcrumbExists) {
          store.setBreadcrumbList(currentMenu)
        }
        // 确保正确的菜单层级状态
        updateMenuHierarchy(currentMenu, menus)
      }
    }
  } else if (currentMenuUrl.value && currentMenuUrl.value !== currentRoutePath) {
    // 如果保存的URL与当前路由不一致，跳转到保存的URL
    router.push(currentMenuUrl.value)
  } else if (activeTopMenu.menuUrl === '/HomePage') {
    // 默认跳转到首页
    router.push('/HomePage')
  }
}

// 更新菜单层级状态
const updateMenuHierarchy = (targetMenu: any, menus: any[]) => {
  // 查找目标菜单所在的层级
  for (const topMenu of menus) {
    if (topMenu.menuId === targetMenu.menuId) {
      // 是顶级菜单
      store.setTopCurrentMenu(topMenu)
      return
    }

    if (topMenu.children) {
      for (const leftMenu of topMenu.children) {
        if (leftMenu.menuId === targetMenu.menuId) {
          // 是二级菜单
          store.setTopCurrentMenu(topMenu)
          store.setCurrentLeftMenu(leftMenu)
          return
        }

        if (leftMenu.children) {
          for (const thirdMenu of leftMenu.children) {
            if (thirdMenu.menuId === targetMenu.menuId) {
              // 是三级菜单
              store.setTopCurrentMenu(topMenu)
              store.setCurrentLeftMenu(leftMenu)
              leftMenuList.value = topMenu.children || []
              thirdMenuList.value = leftMenu.children || []
              return
            }
          }
        }
      }
    }
  }
}

// 根据URL查找菜单项
const findMenuByUrl = (url: string, menuList: any[]): any => {
  for (const topMenu of menuList) {
    if (topMenu.menuUrl === url) return topMenu

    if (topMenu.children) {
      for (const leftMenu of topMenu.children) {
        if (leftMenu.menuUrl === url) return leftMenu

        if (leftMenu.children) {
          for (const thirdMenu of leftMenu.children) {
            if (thirdMenu.menuUrl === url) return thirdMenu
          }
        }
      }
    }
  }
  return null
}

listAllMenuForSecurity({ menuCode: 'ZFPT' }).then((res: any) => {
  store.setMenuList(res.menus)
  initializeMenuState(res.menus, res.user)
})

// 监听路由变化，确保菜单状态与路由同步
watch(() => route.path, (newPath) => {
  // 简化路由监听逻辑，避免冲突
  if (menuList.value.length > 0 && newPath !== '/' && newPath !== currentMenuUrl.value) {
    const menu = findMenuByUrl(newPath, menuList.value)
    if (menu) {
      store.setCurrentMenuUrl(newPath)
    }
  }
}, { immediate: false })

// const breadcrumbList = ref<any[]>([]) //面包屑

// const currentMenuUrl = ref()

// const currentMenu = ref() //当前选中的菜单
const menuclick = (menu: any) => {
  if (menu.menuUrl) {
    breadcrumbChange(menu)
    store.setCurrentMenuUrl(menu.menuUrl)
    router.push(menu.menuUrl)
    window.sessionStorage.removeItem('searchData')
  }
}

const breadcrumbChange = (menu: any) => {
  // 使用store的方法添加面包屑
  store.setBreadcrumbList(menu)
}

// 点击面包屑
const breadcrumbClick = (menu: any) => {
  store.setCurrentMenuUrl(menu.menuUrl)
  router.push(menu.menuUrl)
  window.sessionStorage.removeItem('searchData')
}

// 删除面包屑
const deleteBreadcrumb = (menuUrl: any) => {
  const index = breadcrumbList.value.findIndex((item: { menuUrl: string }) => item.menuUrl === menuUrl)
  if (menuUrl === currentMenuUrl.value) {
    if (index === 0) {
      if (breadcrumbList.value.length > 1) {
        store.setCurrentMenuUrl(breadcrumbList.value[index + 1].menuUrl)
        router.push(breadcrumbList.value[index + 1].menuUrl)
        store.removeBreadcrumbItem(menuUrl)
      }
    } else {
      store.setCurrentMenuUrl(breadcrumbList.value[index - 1].menuUrl)
      router.push(breadcrumbList.value[index - 1].menuUrl)
      store.removeBreadcrumbItem(menuUrl)
    }
  } else {
    store.removeBreadcrumbItem(menuUrl)
  }
  window.sessionStorage.removeItem('searchData')
}
const leftMenuClick = () => {
  if (!pageJump.value){
    store.setCurrentMenuUrl('')
  }
}

// 计算属性：判断是否应该显示MainMenu还是router-view
const shouldShowMainMenu = computed(() => {
  // 如果当前URL是首页，不显示MainMenu
  if (currentMenuUrl.value === '/HomePage') {
    return false
  }

  // 如果有当前菜单URL且不为空，显示router-view
  if (currentMenuUrl.value && currentMenuUrl.value.trim() !== '') {
    return false
  }

  // 关键修复：如果当前路由是具体页面（不是根路径和首页），也显示router-view
  const currentPath = route.path
  if (currentPath && currentPath !== '/' && currentPath !== '/HomePage') {
    return false
  }

  // 默认显示MainMenu（三级菜单选择界面）
  return true
})

provide('router', router) // 定义路由 所有页面都能用路由
provide('route', route) // 定义本页路由 所有页面都能用路由

// 页面加载完成后检查状态
onMounted(() => {
  // 检查并修复状态
  setTimeout(() => {
    const currentPath = route.path
    if (currentPath && currentPath !== '/' && currentPath !== '/HomePage') {
      // 如果在具体页面但currentMenuUrl为空，设置它
      if (!currentMenuUrl.value) {
        store.setCurrentMenuUrl(currentPath)

        // 如果菜单已加载，尝试添加面包屑
        if (menuList.value.length > 0) {
          const menu = findMenuByUrl(currentPath, menuList.value)
          if (menu) {
            const breadcrumbExists = breadcrumbList.value.some((item: any) => item.menuUrl === currentPath)
            if (!breadcrumbExists) {
              store.setBreadcrumbList(menu)
            }
          }
        }
      }
    }
  }, 200) // 增加延迟确保菜单数据加载完成
})

</script>

<template>
  <div class="project-layout">
    <el-container>
      <el-header height="auto">
        <TopComp v-model:leftMenuList="leftMenuList" v-if="userInfo" :menuList="menuList" :userInfo="userInfo" />
      </el-header>
      <el-container v-if="leftMenuList === '/HomePage'">
        <router-view />
      </el-container>
      <el-container v-show="leftMenuList !== '/HomePage'">
        <el-aside width="270px">
          <MenuComp @left-menu-click="leftMenuClick"  v-model:thirdMenuList="thirdMenuList" :menuList="leftMenuList || []" />
        </el-aside>
        <div class="breadcrumb">
          <img class="breadcrumb-bg breadcrumb-left" src="./assets/images/breadcrumb-left.png" alt="" >
          <div class="breadcrumb-bg breadcrumb-center" />
          <img class="breadcrumb-bg breadcrumb-right" src="./assets/images/breadcrumb-right.png" alt="" >
          <img class="address" src="./assets/images/address.png" alt="" >
          <div class="breadcrumb-list">
            <el-tabs v-model="currentMenuUrl" type="card" class="menu-tabs" :closable="breadcrumbList && breadcrumbList.length > 1" @tab-remove="deleteBreadcrumb">
              <el-tab-pane v-for="item in breadcrumbList" :key="item.menuUrl" :label="item.menuName" :name="item.menuUrl">
                <template #label>
                  <div class="tab-title menu-top" :class="currentMenuUrl === item['menuUrl'] ? 'menu-active' : ''" @click="breadcrumbClick(item)">
                    {{ `${item['menuPName'] ? item['menuPName'] + ' - ' + item['menuName'] : item['menuName']}` }}
                  </div>
                </template>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
        <el-main>
          <div class="main-content">
            <!-- 临时调试：显示当前状态 -->
            <div style="background: #f0f0f0; padding: 10px; margin-bottom: 10px; font-size: 12px;">
              调试信息:
              currentMenuUrl: {{ currentMenuUrl }},
              route.path: {{ route.path }},
              shouldShowMainMenu: {{ shouldShowMainMenu }}
            </div>

            <MainMenu @menuclick="menuclick" :menuList="thirdMenuList || []" v-if="shouldShowMainMenu" />
            <router-view v-slot="{ Component, route }" v-else>
              <component v-if="Component" :is="Component" :key="route.fullPath" />
            </router-view>
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<style lang="scss" scoped>
  .project-layout {
    width: 100vw;
    height: 100vh;
    position: relative;
    display: flex;
    background-image: url(./assets/images/top-banner.png);
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    padding: 0 20px 20px;
    background-color: #e8f0fb;
    .el-aside {
      overflow: hidden;
    }
    .el-container {
      overflow-y: auto;
    }
    .el-header {
      padding: 0;
    }
    .breadcrumb {
      height: 50px;
      position: relative;
      display: flex;
      align-items: center;
      padding: 0 80px;
      gap: 10px;
      position: absolute;
      left: 260px;
      right: 20px;
      .address {
        width: 37px;
        height: 37px;
        z-index: 9;
      }
      .breadcrumb-list {
        width: calc(100% - 40px);
        .el-tabs {
          width: 100%;
          :deep {
            .el-tabs__header {
              margin-bottom: 0;
              border: none;
            }
            .is-scrollable {
              padding: 0 30px 0 20px;
            }
            .el-tabs__nav-prev,
            .el-tabs__nav-next {
              color: #fff;
              font-size: 16px;
              z-index: 9;
            }
            .el-tabs__nav {
              border: none;
            }
            .el-tabs__item {
              border: none;
              background: #3b8bff;
              color: #fff;
              margin-left: 10px;
              border-radius: 4px;
              padding-left: 0 !important;
              padding-right: 20px;
              &:first-child {
                margin-top: 0;
              }
              &.is-active {
                border: 1px solid #ffdd53;
                background: #1d82ff;
                color: #ffdd53;
              }
              .tab-title {
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                padding-left: 20px;
              }
            }
          }
        }
      }
      .breadcrumb-bg {
        position: absolute;
        top: 0;
        bottom: 0;
        height: 50px;
        background-size: 100% 100% !important;
      }
      .breadcrumb-left {
        width: 70px;
        left: 0;
      }
      .breadcrumb-center {
        left: 70px;
        right: 70px;
        background: url(./assets/images/breadcrumb-center.png) repeat center center;
      }
      .breadcrumb-right {
        width: 70px;
        right: 0;
      }
    }
    .el-main {
      position: relative;
      box-sizing: border-box;
      padding: 0;
      display: flex;
      flex-direction: column;
      padding-left: 10px;
      .main-content {
        padding: 20px;
        margin-top: 60px;
        background: #ffffff;
        border-radius: 10px;
        position: relative;
        box-sizing: border-box;
        overflow: hidden;
        flex: 1;
      }
    }
  }
</style>

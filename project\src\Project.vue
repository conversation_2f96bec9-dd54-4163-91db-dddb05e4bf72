<script setup lang="ts">
defineOptions({ name: 'Project' })
import MenuComp from './components/menuComp'
import TopComp from './components/topComp'
import MainMenu from './components/mainMenu'
import { useRouter, useRoute } from 'vue-router'

const route = useRoute()
const router = useRouter()

import { projectStore } from './store/index'
const store = projectStore()
import { storeToRefs } from 'pinia'
const { currentMenuUrl, pageJump, breadcrumbList, topCurrentMenu, currentLeftMenu } = storeToRefs(store)

import { listAllMenuForSecurity } from './api/home'

const leftMenuList = ref() //左侧二级菜单
const thirdMenuList = ref() //三级菜单

const menuList = ref()
const userInfo = ref()

// 初始化菜单状态
const initializeMenuState = (menus: any[], user: any) => {
  menuList.value = menus
  userInfo.value = user
  window.sessionStorage.setItem('userLoginName', user.userLoginName)

  // 如果store中已有保存的状态，则恢复状态
  if (topCurrentMenu.value && topCurrentMenu.value.menuId) {
    // 恢复顶部菜单状态
    const savedTopMenu = menus.find((menu: any) => menu.menuId === topCurrentMenu.value.menuId)
    if (savedTopMenu) {
      if (savedTopMenu.menuUrl === '/HomePage') {
        leftMenuList.value = '/HomePage'
        // 如果当前有保存的路由且不是首页，则跳转到保存的路由
        if (currentMenuUrl.value && currentMenuUrl.value !== '/HomePage') {
          router.push(currentMenuUrl.value)
        } else {
          router.push('/HomePage')
        }
      } else {
        // 恢复左侧菜单
        leftMenuList.value = savedTopMenu.children || []

        // 恢复三级菜单
        if (currentLeftMenu.value && currentLeftMenu.value.menuId) {
          const savedLeftMenu = savedTopMenu.children?.find((menu: any) => menu.menuId === currentLeftMenu.value.menuId)
          if (savedLeftMenu) {
            thirdMenuList.value = savedLeftMenu.children || []
          }
        }

        // 如果有保存的当前路由，则跳转到该路由
        if (currentMenuUrl.value) {
          router.push(currentMenuUrl.value)
        }
      }

      // 验证面包屑数据的有效性
      if (breadcrumbList.value && breadcrumbList.value.length > 0) {
        // 检查面包屑中的菜单是否仍然存在于当前菜单结构中
        const validBreadcrumbs = breadcrumbList.value.filter((breadcrumb: any) => {
          return validateMenuExists(breadcrumb, menus)
        })

        // 如果有无效的面包屑，更新面包屑列表
        if (validBreadcrumbs.length !== breadcrumbList.value.length) {
          store.setBreadcrumbListDirect(validBreadcrumbs)
        }
      }
    }
  } else {
    // 没有保存状态，使用默认逻辑
    store.setTopCurrentMenu(menus[0])
    if (menus[0].menuUrl === '/HomePage') {
      leftMenuList.value = '/HomePage'
      router.push('/HomePage')
    } else {
      leftMenuList.value = menus[0].children || []
    }
  }
}

// 验证菜单是否存在于菜单结构中
const validateMenuExists = (menu: any, menuList: any[]): boolean => {
  for (const topMenu of menuList) {
    if (topMenu.menuId === menu.menuId) return true

    if (topMenu.children) {
      for (const leftMenu of topMenu.children) {
        if (leftMenu.menuId === menu.menuId) return true

        if (leftMenu.children) {
          for (const thirdMenu of leftMenu.children) {
            if (thirdMenu.menuId === menu.menuId) return true
          }
        }
      }
    }
  }
  return false
}

listAllMenuForSecurity({ menuCode: 'ZFPT' }).then((res: any) => {
  store.setMenuList(res.menus)
  initializeMenuState(res.menus, res.user)
})

// const breadcrumbList = ref<any[]>([]) //面包屑

// const currentMenuUrl = ref()

// const currentMenu = ref() //当前选中的菜单
const menuclick = (menu: any) => {
  if (menu.menuUrl) {
    breadcrumbChange(menu)
    store.setCurrentMenuUrl(menu.menuUrl)
    router.push(menu.menuUrl)
    window.sessionStorage.removeItem('searchData')
  }
}

const breadcrumbChange = (menu: any) => {
  // 使用store的方法添加面包屑
  store.setBreadcrumbList(menu)
}

// 点击面包屑
const breadcrumbClick = (menu: any) => {
  store.setCurrentMenuUrl(menu.menuUrl)
  router.push(menu.menuUrl)
  window.sessionStorage.removeItem('searchData')
}

// 删除面包屑
const deleteBreadcrumb = (menuUrl: any) => {
  const index = breadcrumbList.value.findIndex((item: { menuUrl: string }) => item.menuUrl === menuUrl)
  if (menuUrl === currentMenuUrl.value) {
    if (index === 0) {
      if (breadcrumbList.value.length > 1) {
        store.setCurrentMenuUrl(breadcrumbList.value[index + 1].menuUrl)
        router.push(breadcrumbList.value[index + 1].menuUrl)
        store.removeBreadcrumbItem(menuUrl)
      }
    } else {
      store.setCurrentMenuUrl(breadcrumbList.value[index - 1].menuUrl)
      router.push(breadcrumbList.value[index - 1].menuUrl)
      store.removeBreadcrumbItem(menuUrl)
    }
  } else {
    store.removeBreadcrumbItem(menuUrl)
  }
  window.sessionStorage.removeItem('searchData')
}
const leftMenuClick = () => {
  if (!pageJump.value){
    store.setCurrentMenuUrl('')
  }
}

// 计算属性：判断是否应该显示MainMenu还是router-view
const shouldShowMainMenu = computed(() => {
  // 如果没有当前菜单URL，显示MainMenu
  if (!currentMenuUrl.value) {
    return true
  }

  // 如果当前URL是首页，不显示MainMenu
  if (currentMenuUrl.value === '/HomePage') {
    return false
  }

  // 如果有面包屑且当前URL在面包屑中，显示router-view
  if (breadcrumbList.value.length > 0) {
    const exists = breadcrumbList.value.some((item: any) => item.menuUrl === currentMenuUrl.value)
    return !exists
  }

  return false
})

provide('router', router) // 定义路由 所有页面都能用路由
provide('route', route) // 定义本页路由 所有页面都能用路由
</script>

<template>
  <div class="project-layout">
    <el-container>
      <el-header height="auto">
        <TopComp v-model:leftMenuList="leftMenuList" v-if="userInfo" :menuList="menuList" :userInfo="userInfo" />
      </el-header>
      <el-container v-if="leftMenuList === '/HomePage'">
        <router-view />
      </el-container>
      <el-container v-show="leftMenuList !== '/HomePage'">
        <el-aside width="270px">
          <MenuComp @left-menu-click="leftMenuClick"  v-model:thirdMenuList="thirdMenuList" :menuList="leftMenuList || []" />
        </el-aside>
        <div class="breadcrumb">
          <img class="breadcrumb-bg breadcrumb-left" src="./assets/images/breadcrumb-left.png" alt="" >
          <div class="breadcrumb-bg breadcrumb-center" />
          <img class="breadcrumb-bg breadcrumb-right" src="./assets/images/breadcrumb-right.png" alt="" >
          <img class="address" src="./assets/images/address.png" alt="" >
          <div class="breadcrumb-list">
            <el-tabs v-model="currentMenuUrl" type="card" class="menu-tabs" :closable="breadcrumbList && breadcrumbList.length > 1" @tab-remove="deleteBreadcrumb">
              <el-tab-pane v-for="item in breadcrumbList" :key="item.menuUrl" :label="item.menuName" :name="item.menuUrl">
                <template #label>
                  <div class="tab-title menu-top" :class="currentMenuUrl === item['menuUrl'] ? 'menu-active' : ''" @click="breadcrumbClick(item)">
                    {{ `${item['menuPName'] ? item['menuPName'] + ' - ' + item['menuName'] : item['menuName']}` }}
                  </div>
                </template>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
        <el-main>
          <div class="main-content">
            <MainMenu @menuclick="menuclick" :menuList="thirdMenuList || []" v-if="shouldShowMainMenu" />
            <router-view v-slot="{ Component, route }" v-else>
              <component v-if="Component" :is="Component" :key="route.fullPath" />
            </router-view>
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<style lang="scss" scoped>
  .project-layout {
    width: 100vw;
    height: 100vh;
    position: relative;
    display: flex;
    background-image: url(./assets/images/top-banner.png);
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    padding: 0 20px 20px;
    background-color: #e8f0fb;
    .el-aside {
      overflow: hidden;
    }
    .el-container {
      overflow-y: auto;
    }
    .el-header {
      padding: 0;
    }
    .breadcrumb {
      height: 50px;
      position: relative;
      display: flex;
      align-items: center;
      padding: 0 80px;
      gap: 10px;
      position: absolute;
      left: 260px;
      right: 20px;
      .address {
        width: 37px;
        height: 37px;
        z-index: 9;
      }
      .breadcrumb-list {
        width: calc(100% - 40px);
        .el-tabs {
          width: 100%;
          :deep {
            .el-tabs__header {
              margin-bottom: 0;
              border: none;
            }
            .is-scrollable {
              padding: 0 30px 0 20px;
            }
            .el-tabs__nav-prev,
            .el-tabs__nav-next {
              color: #fff;
              font-size: 16px;
              z-index: 9;
            }
            .el-tabs__nav {
              border: none;
            }
            .el-tabs__item {
              border: none;
              background: #3b8bff;
              color: #fff;
              margin-left: 10px;
              border-radius: 4px;
              padding-left: 0 !important;
              padding-right: 20px;
              &:first-child {
                margin-top: 0;
              }
              &.is-active {
                border: 1px solid #ffdd53;
                background: #1d82ff;
                color: #ffdd53;
              }
              .tab-title {
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                padding-left: 20px;
              }
            }
          }
        }
      }
      .breadcrumb-bg {
        position: absolute;
        top: 0;
        bottom: 0;
        height: 50px;
        background-size: 100% 100% !important;
      }
      .breadcrumb-left {
        width: 70px;
        left: 0;
      }
      .breadcrumb-center {
        left: 70px;
        right: 70px;
        background: url(./assets/images/breadcrumb-center.png) repeat center center;
      }
      .breadcrumb-right {
        width: 70px;
        right: 0;
      }
    }
    .el-main {
      position: relative;
      box-sizing: border-box;
      padding: 0;
      display: flex;
      flex-direction: column;
      padding-left: 10px;
      .main-content {
        padding: 20px;
        margin-top: 60px;
        background: #ffffff;
        border-radius: 10px;
        position: relative;
        box-sizing: border-box;
        overflow: hidden;
        flex: 1;
      }
    }
  }
</style>

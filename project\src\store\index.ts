import { defineStore } from 'pinia'

// 从sessionStorage获取保存的状态
const getStoredState = () => {
  try {
    const storedState = sessionStorage.getItem('projectMenuState')
    console.log('从sessionStorage读取状态:', storedState)
    return storedState ? JSON.parse(storedState) : {}
  } catch (error) {
    console.error('Failed to parse stored state:', error)
    return {}
  }
}

// 保存状态到sessionStorage
const saveStateToStorage = (state: any) => {
  try {
    const stateToSave = {
      topCurrentMenu: state.topCurrentMenu,
      breadcrumbList: state.breadcrumbList,
      currentMenuUrl: state.currentMenuUrl,
      currentLeftMenu: state.currentLeftMenu,
      menuList: state.menuList
    }
    console.log('保存状态到sessionStorage:', stateToSave)
    sessionStorage.setItem('projectMenuState', JSON.stringify(stateToSave))
  } catch (error) {
    console.error('Failed to save state to storage:', error)
  }
}

export const projectStore = defineStore('projectStore', {
  state: () => {
    const storedState = getStoredState()
    return {
      currentLoginDate: null as any, // 待办总数
      enforcementDocuments: [] as any, // 执法文书
      topCurrentMenu: storedState.topCurrentMenu || {} as any, // 顶部菜单
      menuList: storedState.menuList || [] as any, // 菜单列表,
      breadcrumbList: storedState.breadcrumbList || [] as any, // 面包屑列表
      currentMenuUrl: storedState.currentMenuUrl || '' as any, // 当前菜单url
      currentLeftMenu: storedState.currentLeftMenu || {} as any, // 左侧菜单激活
      pageJump: false as boolean // 页面跳转
    }
  },
  //适合计算属性 顾名思义 依赖于state的值计算出来的属性
  getters: {
    // leftActiveMenuId: (state) => {
    //   return state.leftActiveMenu?.menuId
    // }
  },
  //适合定义业务逻辑
  actions: {
    setCurrentLoginDate(date: string | undefined) {
      this.currentLoginDate = date
    },
    setEnforcementDocuments(list: any) {
      // 根据 templateType 去重
      if (Array.isArray(list)) {
        const map = new Map()
        list.forEach((item: any) => {
          if (item && item.templateType !== undefined) {
            map.set(item.templateType, item)
          }
        })
        this.enforcementDocuments = Array.from(map.values())
      } else {
        this.enforcementDocuments = []
      }
    },
    setTopCurrentMenu(menu: any) {
      this.topCurrentMenu = menu
      saveStateToStorage(this)
    },
    setMenuList(list: any) {
      this.menuList = list
      saveStateToStorage(this)
    },
    setCurrentMenuUrl(url: string) {
      this.currentMenuUrl = url
      saveStateToStorage(this)
    },
    setCurrentLeftMenu(menu: any) {
      this.currentLeftMenu = menu
      saveStateToStorage(this)
    },
    setPageJump(status: boolean) {
      this.pageJump = status
    },
    setBreadcrumbList(menu: any) {
      const exists = this.breadcrumbList.some((item: any) => item.menuId === menu.menuId)
      if (!exists) {
        this.breadcrumbList = [...this.breadcrumbList, menu]
      }
      saveStateToStorage(this)
    },
    // 新增：直接设置面包屑列表
    setBreadcrumbListDirect(list: any[]) {
      this.breadcrumbList = list
      saveStateToStorage(this)
    },
    // 新增：删除面包屑项
    removeBreadcrumbItem(menuUrl: string) {
      const index = this.breadcrumbList.findIndex((item: any) => item.menuUrl === menuUrl)
      if (index > -1) {
        this.breadcrumbList.splice(index, 1)
        saveStateToStorage(this)
      }
    },
    // 新增：清除所有状态
    clearMenuState() {
      this.topCurrentMenu = {}
      this.breadcrumbList = []
      this.currentMenuUrl = ''
      this.currentLeftMenu = {}
      sessionStorage.removeItem('projectMenuState')
    }
  }
})

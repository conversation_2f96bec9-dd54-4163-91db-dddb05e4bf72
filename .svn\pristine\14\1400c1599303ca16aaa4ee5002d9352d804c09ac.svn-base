<script setup lang="ts">
import ElFormItemValid from './formItemValid'
import { is, uqAlert } from '../hooks'
defineOptions({ name: 'UqNumber' })
const attrs = useAttrs()
const props = withDefaults(
  defineProps<{
    prop?: string
    eProp?: string
    endVal?: any
    label?: any
    rules?: string
    stuff?: string
    bottom?: string | number
    precision?: any // 精度
    type?: any
    link?: boolean // 是否是有开始数字结束数字
    labelWidth?: string | number
    modelValue: any
  }>(),
  {
    label: '名称',
    link: false,
    type: 'number',
    prop: '',
    stuff: '-'
  }
)

const dataValue:any = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emits('update:modelValue', value)
  }
})
const endValue:any = computed({
  get() {
    return props.endVal
  },
  set(value) {
    emits('update:endVal', value)
  }
})
// 开始数字切换的时候
const changeStartFun = (val: any) => {
  if (val > endValue.value && is.Number(endValue.value) && is.Number(val)) {
    dataValue.value = null
    uqAlert.warning('开始值不能大于结束值')
  }
  emits('change', val)
}
// 结束数字切换的时候
const changeEndFun = (val: any) => {
  if (val < dataValue.value && is.Number(dataValue.value) && is.Number(val)) {
    endValue.value = null
    uqAlert.warning('结束值不能小于开始值')
  }
  emits('eChange', val)
}

const changeFun = (val: any) => {
  emits('change', val)
}
const emits = defineEmits(['change', 'update:modelValue', 'update:endVal', 'eChange'])
</script>

<template>
  <!--用法示例 可参考uq-date-->
  <template v-if="link">
    <template v-if="typeof label === 'string'">
      <el-form-item-valid :label="label" :prop="prop" v-bind="attrs" :bottom="bottom" :required="rules?.includes('required')" :rules="rules" :labelWidth="labelWidth" style="width: 100%">
        <template v-if="$slots.label" #label>
          <slot name="label" />
        </template>
        <div class="date-time-link">
          <div class="time-item start_time">
            <el-form-item-valid label-width="0px" label="开始数字" :bottom="bottom" :prop="prop" :required="rules?.includes('required')" :rules="rules">
              <el-input-number v-model="dataValue" :precision="precision" :controls="false"  @change="(val: any) => changeStartFun(val)" style="width: 100%" />
            </el-form-item-valid>
          </div>
          <div v-if="stuff" :span="2" class="uq-center middle_stuff">{{ stuff }}</div>
          <div class="time-item end_time">
            <el-form-item-valid label="结束数字" label-width="0px" :bottom="bottom" :prop="eProp" :required="rules?.includes('required')" :rules="rules">
              <el-input-number v-model="endValue" :precision="precision" :controls="false"  @change="(val: any) => changeEndFun(val)" style="width: 100%" />
            </el-form-item-valid>
          </div>
        </div>
      </el-form-item-valid>
    </template>
    <template v-else>
      <div class="date-time-link">
        <div class="time-item start_time">
          <el-form-item-valid
            :label="label[0]"
            :label-width="labelWidth"
            style="width: 100%"
            :prop="prop"
            :bottom="bottom"
            :required="rules?.includes('required')"
            :rules="rules"
          >
            <template v-if="$slots.label" #label>
              <slot name="label" />
            </template>
            <el-input-number v-model="dataValue" :precision="precision" :controls="false"  @change="(val: any) => changeStartFun(val)" />
          </el-form-item-valid>
        </div>
        <div v-if="stuff" :span="2" class="uq-center middle_stuff">{{ stuff }}</div>
        <div class="time-item end_time">
          <el-form-item-valid
            :label="label[1]"
            :label-width="labelWidth"
            style="width: 100%"
            :prop="eProp"
            :bottom="bottom"
            :required="rules?.includes('required')"
            :rules="rules"
          >
            <template v-if="$slots.label" #label>
              <slot name="label" />
            </template>
            <el-input-number v-model="endValue" :precision="precision" :controls="false"  @change="(val: any) => changeEndFun(val)" />
          </el-form-item-valid>
        </div>
      </div>
    </template>
  </template>

  <el-form-item-valid v-else :label="label" :prop="prop" :label-width="labelWidth" :required="rules?.includes('required')" :rules="rules" class="custom-date-picker-single">
    <template v-if="$slots.label" #label>
      <slot name="label" />
    </template>
    <el-input-number v-model="dataValue" :precision="precision" :controls="false"  @change="(val: any) => changeFun(val)" />
  </el-form-item-valid>
</template>

<style lang="scss" scoped>
.date-time-link {
  width: 100%;
  display: inline-flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 5px;
  .time-item {
    width: calc(50% - 10px);
    :deep(.el-input.el-input--default) {
      width: 100%;
    }
  }
  :deep(.el-form-item__label) {
    overflow: hidden;
    padding: 0;
  }
}
.custom-date-picker-single {
  :deep(.el-input.el-input--default) {
    width: 100%;
  }
}
</style>
